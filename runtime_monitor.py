
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时数据监控工具
实时监控Streamlit应用中的数据变化，追踪问题字段
"""

import streamlit as st
import pandas as pd
import numpy as np
import json
from datetime import datetime

def monitor_data_changes():
    """监控数据变化"""
    st.write("🔍 实时数据监控")

    # 检查session_state中的数据
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        st.write(f"📊 当前数据形状: {df.shape}")
        st.write(f"📋 当前列名: {list(df.columns)}")

        # 检查问题字段
        problematic_fields = ['销售额_start', '销售额_end']
        issues_found = []

        for field in problematic_fields:
            if field in df.columns:
                st.error(f"❌ 发现问题字段: {field}")
                field_data = df[field]
                st.write(f"数据类型: {field_data.dtype}")
                st.write(f"无穷大值数量: {np.isinf(field_data).sum()}")
                st.write(f"NaN值数量: {field_data.isnull().sum()}")
                st.write("示例值:")
                st.write(field_data.head())
                issues_found.append(field)
            else:
                st.success(f"✅ 未发现字段: {field}")

        # 检查数值列中的异常值
        st.subheader("📊 数值列异常值检查")
        numeric_cols = df.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            inf_count = np.isinf(df[col]).sum()
            nan_count = df[col].isnull().sum()

            if inf_count > 0 or nan_count > 0:
                st.warning(f"⚠️ 列 '{col}': 无穷大值={inf_count}, NaN值={nan_count}")
            else:
                st.success(f"✅ 列 '{col}': 数据正常")

        # 显示完整数据预览
        with st.expander("📋 完整数据预览"):
            st.dataframe(df)

        return len(issues_found) == 0
    else:
        st.info("⚠️ 当前无数据")
        return True

def check_metadata_state():
    """检查元数据状态"""
    st.subheader("🔍 元数据状态检查")

    try:
        from metadata_manager import metadata_manager

        # 获取所有表格
        tables = metadata_manager.get_all_tables()
        st.write(f"📊 元数据表格数量: {len(tables)}")
        st.write(f"📋 表格列表: {tables}")

        # 检查是否包含问题字段
        problematic_fields = ['销售额_start', '销售额_end']
        found_issues = []

        for table_name in tables:
            table_metadata = metadata_manager.get_table_metadata(table_name)
            if table_metadata:
                for field in problematic_fields:
                    if field in table_metadata.columns:
                        found_issues.append(f"{table_name}.{field}")
                        st.error(f"❌ 元数据包含问题字段: {table_name}.{field}")

        if not found_issues:
            st.success("✅ 元数据状态正常")

        return len(found_issues) == 0

    except Exception as e:
        st.error(f"❌ 检查元数据失败: {e}")
        return False

def check_uploaded_files():
    """检查上传文件"""
    st.subheader("📁 上传文件检查")

    import os
    upload_dir = "uploaded_files"

    if os.path.exists(upload_dir):
        files = [f for f in os.listdir(upload_dir) if f.endswith('.csv')]
        st.write(f"📊 CSV文件数量: {len(files)}")

        for file_name in files:
            file_path = os.path.join(upload_dir, file_name)
            try:
                df = pd.read_csv(file_path)
                st.write(f"📄 {file_name}: {df.shape}")

                # 检查问题字段
                problematic_fields = ['销售额_start', '销售额_end']
                has_issues = any(field in df.columns for field in problematic_fields)

                if has_issues:
                    st.error(f"❌ {file_name} 包含问题字段")
                    for field in problematic_fields:
                        if field in df.columns:
                            st.write(f"   - {field}")
                else:
                    st.success(f"✅ {file_name} 正常")

            except Exception as e:
                st.error(f"❌ 读取 {file_name} 失败: {e}")
    else:
        st.warning("⚠️ 上传目录不存在")

def clear_caches():
    """清理缓存"""
    st.subheader("🧹 缓存清理")

    if st.button("清理Streamlit缓存"):
        st.cache_data.clear()
        st.cache_resource.clear()
        st.success("✅ Streamlit缓存已清理")
        st.rerun()

    if st.button("重置session_state"):
        for key in list(st.session_state.keys()):
            del st.session_state[key]
        st.success("✅ session_state已重置")
        st.rerun()

def generate_diagnostic_report():
    """生成诊断报告"""
    st.subheader("📊 诊断报告")

    report = {
        "timestamp": datetime.now().isoformat(),
        "session_state_data": None,
        "metadata_tables": None,
        "uploaded_files": None
    }

    # 收集session_state信息
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        report["session_state_data"] = {
            "shape": df.shape,
            "columns": list(df.columns),
            "has_problematic_fields": any(field in df.columns for field in ['销售额_start', '销售额_end'])
        }

    # 收集元数据信息
    try:
        from metadata_manager import metadata_manager
        tables = metadata_manager.get_all_tables()
        report["metadata_tables"] = tables
    except:
        report["metadata_tables"] = "无法获取"

    # 收集文件信息
    import os
    if os.path.exists("uploaded_files"):
        files = [f for f in os.listdir("uploaded_files") if f.endswith('.csv')]
        report["uploaded_files"] = files

    st.json(report)

    # 提供下载链接
    report_json = json.dumps(report, indent=2, ensure_ascii=False)
    st.download_button(
        label="� 下载诊断报告",
        data=report_json,
        file_name=f"diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        mime="application/json"
    )

def fix_chart_issues():
    """修复图表问题"""
    st.subheader("🔧 图表问题修复")

    if st.button("🚨 紧急修复图表问题", type="primary"):
        with st.spinner("正在修复图表问题..."):
            success_count = 0

            # 1. 清理session_state数据
            if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
                df = st.session_state.current_data

                # 检查并移除问题字段
                problematic_fields = ['销售额_start', '销售额_end']
                removed_fields = []

                for field in problematic_fields:
                    if field in df.columns:
                        df = df.drop(columns=[field])
                        removed_fields.append(field)

                if removed_fields:
                    st.success(f"✅ 已移除问题字段: {removed_fields}")
                    success_count += 1

                # 清理数值列中的无穷大值
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                cleaned_cols = []

                for col in numeric_cols:
                    if np.isinf(df[col]).any():
                        df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                        df[col] = df[col].fillna(0)
                        cleaned_cols.append(col)

                if cleaned_cols:
                    st.success(f"✅ 已清理无穷大值: {cleaned_cols}")
                    success_count += 1

                # 更新session_state
                st.session_state.current_data = df

            # 2. 清理元数据
            try:
                from metadata_manager import metadata_manager
                tables = metadata_manager.get_all_tables()

                for table_name in tables:
                    table_metadata = metadata_manager.get_table_metadata(table_name)
                    if table_metadata and hasattr(table_metadata, 'columns'):
                        problematic_fields = ['销售额_start', '销售额_end']
                        for field in problematic_fields:
                            if field in table_metadata.columns:
                                # 这里需要实现移除元数据字段的功能
                                st.warning(f"⚠️ 元数据中发现问题字段: {table_name}.{field}")

                success_count += 1
            except Exception as e:
                st.error(f"❌ 清理元数据失败: {e}")

            # 3. 清理缓存
            st.cache_data.clear()
            st.cache_resource.clear()
            success_count += 1

            if success_count > 0:
                st.success(f"🎉 修复完成！共执行了 {success_count} 项修复操作")
                st.info("💡 建议刷新页面以确保修复生效")

                if st.button("🔄 立即刷新页面"):
                    st.rerun()
            else:
                st.warning("⚠️ 未发现需要修复的问题")

def advanced_data_validation():
    """高级数据验证"""
    st.subheader("🔍 高级数据验证")

    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data

        # 数据质量检查
        st.write("**数据质量检查:**")

        # 检查列名
        col1, col2 = st.columns(2)

        with col1:
            st.write("📋 **列名分析:**")
            for col in df.columns:
                if any(char in str(col) for char in ['_start', '_end']):
                    st.error(f"❌ 可疑列名: {col}")
                elif any(char in str(col) for char in ['销售额', '销售', '金额']):
                    st.success(f"✅ 正常销售字段: {col}")
                else:
                    st.info(f"ℹ️ 其他字段: {col}")

        with col2:
            st.write("🔢 **数值质量分析:**")
            numeric_cols = df.select_dtypes(include=[np.number]).columns

            for col in numeric_cols:
                inf_count = np.isinf(df[col]).sum()
                nan_count = df[col].isnull().sum()
                zero_count = (df[col] == 0).sum()

                if inf_count > 0:
                    st.error(f"❌ {col}: {inf_count} 个无穷大值")
                elif nan_count > 0:
                    st.warning(f"⚠️ {col}: {nan_count} 个空值")
                elif zero_count == len(df):
                    st.warning(f"⚠️ {col}: 全为零值")
                else:
                    st.success(f"✅ {col}: 数据正常")

        # 数据预览
        with st.expander("📊 数据预览"):
            st.dataframe(df.head(10))

        # 数据统计
        with st.expander("📈 数据统计"):
            st.write(df.describe())
    else:
        st.info("⚠️ 当前无数据可验证")

def main():
    """主函数"""
    st.title("�🔍 运行时数据监控工具")
    st.write("实时监控Streamlit应用中的数据状态，追踪销售额_start和销售额_end字段问题")

    # 创建标签页
    tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
        "数据监控", "元数据检查", "文件检查", "缓存清理", "诊断报告", "问题修复", "高级验证"
    ])

    with tab1:
        data_ok = monitor_data_changes()

        if st.button("🔄 刷新数据监控"):
            st.rerun()

    with tab2:
        metadata_ok = check_metadata_state()

    with tab3:
        check_uploaded_files()

    with tab4:
        clear_caches()

    with tab5:
        generate_diagnostic_report()

    with tab6:
        fix_chart_issues()

    with tab7:
        advanced_data_validation()

    # 总体状态
    st.sidebar.header("📊 总体状态")
    if 'data_ok' in locals() and data_ok:
        st.sidebar.success("✅ 数据状态正常")
    else:
        st.sidebar.error("❌ 发现数据问题")

    if 'metadata_ok' in locals() and metadata_ok:
        st.sidebar.success("✅ 元数据状态正常")
    else:
        st.sidebar.warning("⚠️ 元数据可能有问题")

if __name__ == "__main__":
    main()
