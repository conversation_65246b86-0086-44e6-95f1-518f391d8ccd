#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复print()为Streamlit组件的专用工具
直接在代码执行前进行转换
"""

import re
import streamlit as st

def convert_print_to_streamlit_advanced(code):
    """高级print()到Streamlit组件转换器"""
    print("🔄 开始高级print()转换...")
    
    lines = code.split('\n')
    converted_lines = []
    
    for i, line in enumerate(lines):
        original_line = line
        stripped_line = line.strip()
        
        # 获取缩进
        indent = line[:len(line) - len(line.lstrip())]
        
        # 检查是否是print语句
        if stripped_line.startswith('print(') and stripped_line.endswith(')'):
            # 提取print的内容
            print_content = stripped_line[6:-1]  # 移除 'print(' 和 ')'
            
            print(f"  发现print语句: {stripped_line}")
            
            # 智能判断转换方式
            converted_line = None
            
            # 1. 处理变量名（如grouped_data, data等）
            if print_content in ['grouped_data', 'data', 'result', 'df_result']:
                converted_line = f"st.dataframe({print_content}.to_frame() if hasattr({print_content}, 'to_frame') else {print_content}, use_container_width=True)"
                print(f"    → DataFrame显示: {converted_line}")
            
            # 2. 处理带有变量的表达式
            elif any(var in print_content for var in ['grouped_data', 'data', 'result']) and not print_content.startswith('"') and not print_content.startswith("'"):
                # 可能是DataFrame或Series
                converted_line = f"st.dataframe({print_content}.to_frame() if hasattr({print_content}, 'to_frame') else {print_content}, use_container_width=True)"
                print(f"    → DataFrame表达式: {converted_line}")
            
            # 3. 处理f-string格式的结论
            elif print_content.startswith('f"') or print_content.startswith("f'"):
                if any(keyword in print_content for keyword in ['答案', '最高', '最低', '结果', '总计']):
                    converted_line = f"st.success({print_content})"
                    print(f"    → 成功消息: {converted_line}")
                else:
                    converted_line = f"st.info({print_content})"
                    print(f"    → 信息消息: {converted_line}")
            
            # 4. 处理普通字符串
            elif print_content.startswith('"') or print_content.startswith("'"):
                if any(keyword in print_content for keyword in ['数据', '统计', '分析', '结果']):
                    converted_line = f"st.write('**' + {print_content} + '**')"
                    print(f"    → 标题文本: {converted_line}")
                else:
                    converted_line = f"st.write({print_content})"
                    print(f"    → 普通文本: {converted_line}")
            
            # 5. 其他情况
            else:
                converted_line = f"st.write({print_content})"
                print(f"    → 默认显示: {converted_line}")
            
            # 应用转换
            if converted_line:
                converted_lines.append(indent + converted_line)
            else:
                converted_lines.append(original_line)
        
        else:
            # 不是print语句，保持原样
            converted_lines.append(original_line)
    
    converted_code = '\n'.join(converted_lines)
    print("✅ 高级print()转换完成")
    return converted_code

def apply_comprehensive_dataframe_fix(code):
    """应用全面的DataFrame显示修复"""
    print("🔧 应用全面DataFrame显示修复...")
    
    # 1. 先转换print语句
    code = convert_print_to_streamlit_advanced(code)
    
    # 2. 添加DataFrame显示增强
    enhanced_code = add_dataframe_display_enhancements(code)
    
    return enhanced_code

def add_dataframe_display_enhancements(code):
    """添加DataFrame显示增强功能"""
    print("✨ 添加DataFrame显示增强...")
    
    # 在代码开头添加显示增强函数
    enhancement_code = '''
# DataFrame显示增强函数
def display_dataframe_enhanced(data, title="数据结果"):
    """增强的DataFrame显示函数"""
    if hasattr(data, 'to_frame'):
        # Series转DataFrame
        df_display = data.to_frame()
        if len(df_display.columns) == 1 and df_display.columns[0] == 0:
            df_display.columns = [title.replace('数据结果', '数值')]
    elif hasattr(data, 'index') and hasattr(data, 'values'):
        # 其他pandas对象
        df_display = data
    else:
        # 普通数据
        st.write(data)
        return
    
    # 显示标题
    if title != "数据结果":
        st.write(f"**{title}:**")
    
    # 显示DataFrame
    st.dataframe(df_display, use_container_width=True)
    
    # 显示统计信息
    if len(df_display) > 0:
        col1, col2 = st.columns(2)
        with col1:
            st.metric("数据行数", len(df_display))
        with col2:
            if df_display.select_dtypes(include=['number']).shape[1] > 0:
                numeric_col = df_display.select_dtypes(include=['number']).columns[0]
                st.metric("数值总计", f"{df_display[numeric_col].sum():,.0f}")

'''
    
    # 将增强代码添加到原代码前面
    enhanced_code = enhancement_code + '\n' + code
    
    # 替换简单的st.dataframe调用为增强版本
    lines = enhanced_code.split('\n')
    final_lines = []
    
    for line in lines:
        if 'st.dataframe(' in line and 'grouped_data' in line:
            # 替换为增强版本
            indent = line[:len(line) - len(line.lstrip())]
            enhanced_line = indent + 'display_dataframe_enhanced(grouped_data, "各项统计数据")'
            final_lines.append(enhanced_line)
            print(f"  增强: {line.strip()} → {enhanced_line.strip()}")
        else:
            final_lines.append(line)
    
    return '\n'.join(final_lines)

def test_conversion():
    """测试转换功能"""
    test_code = '''
grouped_data = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)
print(grouped_data)
print(f"销售额最高的是: {grouped_data.index[0]}")
'''
    
    print("🧪 测试代码转换:")
    print("原始代码:")
    print(test_code)
    print("\n转换后:")
    converted = apply_comprehensive_dataframe_fix(test_code)
    print(converted)

if __name__ == "__main__":
    test_conversion()
