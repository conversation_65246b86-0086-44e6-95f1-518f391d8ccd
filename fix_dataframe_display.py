#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DataFrame显示问题快速修复脚本
一键修复常见的DataFrame显示问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import re
from pathlib import Path

def fix_dataframe_display_issues():
    """修复DataFrame显示问题的主函数"""
    st.title("🔧 DataFrame显示问题快速修复")
    
    if not hasattr(st.session_state, 'current_data') or st.session_state.current_data is None:
        st.error("❌ 没有找到数据，请先上传数据文件")
        return False
    
    df = st.session_state.current_data
    st.info(f"📊 当前数据: {df.shape[0]}行 × {df.shape[1]}列")
    
    # 显示修复前的数据预览
    with st.expander("🔍 修复前数据预览", expanded=False):
        try:
            st.dataframe(df.head(3), use_container_width=True)
        except Exception as e:
            st.error(f"显示失败: {e}")
            st.text(str(df.head(3)))
    
    # 执行修复
    if st.button("🚀 开始修复", type="primary"):
        with st.spinner("正在修复DataFrame显示问题..."):
            try:
                # 1. 数据清理
                fixed_df = apply_comprehensive_fixes(df)
                
                # 2. 验证修复结果
                validation_results = validate_dataframe(fixed_df)
                
                # 3. 显示修复结果
                st.success("✅ 修复完成！")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.subheader("📊 修复前")
                    st.write(f"- 形状: {df.shape}")
                    st.write(f"- 列数: {len(df.columns)}")
                    
                    # 检查问题
                    numeric_cols = df.select_dtypes(include=[np.number]).columns
                    inf_count = np.isinf(df[numeric_cols]).sum().sum() if len(numeric_cols) > 0 else 0
                    nan_count = df.isnull().sum().sum()
                    
                    st.write(f"- 无穷大值: {inf_count}")
                    st.write(f"- NaN值: {nan_count}")
                    
                    problematic_cols = [col for col in df.columns if any(pattern in str(col) for pattern in ['_start', '_end'])]
                    st.write(f"- 问题列: {len(problematic_cols)}")
                
                with col2:
                    st.subheader("📊 修复后")
                    st.write(f"- 形状: {fixed_df.shape}")
                    st.write(f"- 列数: {len(fixed_df.columns)}")
                    
                    # 检查修复后的问题
                    fixed_numeric_cols = fixed_df.select_dtypes(include=[np.number]).columns
                    fixed_inf_count = np.isinf(fixed_df[fixed_numeric_cols]).sum().sum() if len(fixed_numeric_cols) > 0 else 0
                    fixed_nan_count = fixed_df.isnull().sum().sum()
                    
                    st.write(f"- 无穷大值: {fixed_inf_count}")
                    st.write(f"- NaN值: {fixed_nan_count}")
                    
                    fixed_problematic_cols = [col for col in fixed_df.columns if any(pattern in str(col) for pattern in ['_start', '_end'])]
                    st.write(f"- 问题列: {len(fixed_problematic_cols)}")
                
                # 显示修复后的数据预览
                with st.expander("✨ 修复后数据预览", expanded=True):
                    try:
                        st.dataframe(fixed_df.head(10), use_container_width=True)
                        st.success("✅ DataFrame显示正常！")
                    except Exception as e:
                        st.error(f"❌ 显示仍有问题: {e}")
                        return False
                
                # 显示验证结果
                if validation_results['is_valid']:
                    st.success("🎉 数据验证通过，可以安全显示！")
                else:
                    st.warning("⚠️ 数据验证发现一些问题:")
                    for issue in validation_results['issues']:
                        st.write(f"- {issue}")
                
                # 应用修复
                if st.button("✅ 应用修复到当前数据"):
                    st.session_state.current_data = fixed_df
                    st.success("🎉 修复已应用！数据已更新。")
                    st.balloons()
                    st.rerun()
                
                return True
                
            except Exception as e:
                st.error(f"❌ 修复过程中出现错误: {e}")
                st.code(str(e))
                return False
    
    return False

def apply_comprehensive_fixes(df):
    """应用全面的DataFrame修复"""
    if df is None or df.empty:
        return df
    
    # 创建副本
    fixed_df = df.copy()
    
    # 1. 移除问题列
    problematic_patterns = ['_start', '_end', '_begin', '_finish', '_tmp', '_temp']
    cols_to_remove = []
    
    for col in list(fixed_df.columns):
        col_str = str(col).lower()
        for pattern in problematic_patterns:
            if pattern in col_str and ('销售' in col_str or '金额' in col_str or '数量' in col_str):
                cols_to_remove.append(col)
                break
    
    if cols_to_remove:
        fixed_df = fixed_df.drop(columns=cols_to_remove)
        st.info(f"🗑️ 移除了问题列: {cols_to_remove}")
    
    # 2. 清理列名
    original_columns = list(fixed_df.columns)
    new_columns = []
    
    for col in original_columns:
        # 移除特殊字符，保留中文、英文、数字和下划线
        clean_col = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col))
        # 移除多余的下划线
        clean_col = re.sub(r'_+', '_', clean_col).strip('_')
        new_columns.append(clean_col)
    
    fixed_df.columns = new_columns
    
    # 3. 处理数值列中的异常值
    numeric_cols = fixed_df.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        # 统计异常值
        inf_count = np.isinf(fixed_df[col]).sum()
        nan_count = fixed_df[col].isnull().sum()
        
        if inf_count > 0 or nan_count > 0:
            # 替换无穷大值为NaN
            fixed_df[col] = fixed_df[col].replace([np.inf, -np.inf], np.nan)
            
            # 智能填充策略
            if fixed_df[col].notna().sum() > 0:
                # 如果有有效数据，用中位数填充
                median_val = fixed_df[col].median()
                fixed_df[col] = fixed_df[col].fillna(median_val)
            else:
                # 如果全是NaN，用0填充
                fixed_df[col] = fixed_df[col].fillna(0)
            
            # 确保数据类型正确
            fixed_df[col] = pd.to_numeric(fixed_df[col], errors='coerce').fillna(0)
    
    # 4. 处理文本列中的异常值
    text_cols = fixed_df.select_dtypes(include=['object']).columns
    
    for col in text_cols:
        # 填充空值
        fixed_df[col] = fixed_df[col].fillna('未知')
        # 确保都是字符串
        fixed_df[col] = fixed_df[col].astype(str)
    
    # 5. 处理重复索引
    if fixed_df.index.duplicated().any():
        fixed_df = fixed_df.reset_index(drop=True)
    
    # 6. 移除完全空的行和列
    # 移除全空的行
    fixed_df = fixed_df.dropna(how='all')
    
    # 移除全空的列
    fixed_df = fixed_df.dropna(axis=1, how='all')
    
    return fixed_df

def validate_dataframe(df):
    """验证DataFrame是否适合显示"""
    issues = []
    
    # 检查基本结构
    if df is None:
        issues.append("DataFrame为None")
        return {'is_valid': False, 'issues': issues}
    
    if df.empty:
        issues.append("DataFrame为空")
        return {'is_valid': False, 'issues': issues}
    
    # 检查数值列中的异常值
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        inf_count = np.isinf(df[col]).sum()
        if inf_count > 0:
            issues.append(f"列 '{col}' 仍包含 {inf_count} 个无穷大值")
        
        nan_count = df[col].isnull().sum()
        if nan_count > len(df) * 0.8:  # 超过80%为NaN
            issues.append(f"列 '{col}' 包含过多空值: {nan_count}/{len(df)}")
    
    # 检查列名
    for col in df.columns:
        if any(pattern in str(col).lower() for pattern in ['_start', '_end']):
            issues.append(f"仍存在问题列名: {col}")
    
    # 检查数据类型
    for col in df.columns:
        if df[col].dtype == 'object':
            # 检查是否有混合类型
            unique_types = set(type(x).__name__ for x in df[col].dropna().iloc[:100])
            if len(unique_types) > 2:  # 允许str和NoneType
                issues.append(f"列 '{col}' 包含混合数据类型: {unique_types}")
    
    is_valid = len(issues) == 0
    
    return {
        'is_valid': is_valid,
        'issues': issues,
        'shape': df.shape,
        'columns': list(df.columns),
        'dtypes': df.dtypes.to_dict()
    }

def create_test_dataframe():
    """创建测试DataFrame用于验证修复功能"""
    st.subheader("🧪 创建测试数据")
    
    if st.button("创建包含问题的测试数据"):
        # 创建包含各种问题的测试数据
        test_data = {
            '产品名称@#$': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
            '销售额_start': [25000, np.inf, 20000, 15000, 12000],
            '销售额_end': [30000, 25000, -np.inf, 18000, 15000],
            '销售额': [27500, np.nan, 22000, 16500, 13500],
            '销量': [100, 80, 60, 120, 90],
            '地区': ['北京', '上海', '广州', '深圳', '杭州']
        }
        
        test_df = pd.DataFrame(test_data)
        st.session_state.current_data = test_df
        
        st.success("✅ 测试数据已创建！包含以下问题:")
        st.write("- 列名包含特殊字符")
        st.write("- 包含无穷大值")
        st.write("- 包含NaN值")
        st.write("- 包含问题列名后缀")
        
        st.dataframe(test_df, use_container_width=True)

def main():
    """主函数"""
    st.set_page_config(
        page_title="DataFrame显示修复工具",
        page_icon="🔧",
        layout="wide"
    )
    
    # 创建测试数据选项
    with st.sidebar:
        st.title("🛠️ 工具选项")
        create_test_dataframe()
    
    # 执行修复
    fix_dataframe_display_issues()

if __name__ == "__main__":
    main()
