#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Streamlit 1.45.0版本DataFrame显示问题
针对新版本的默认行为变化进行适配
"""

import streamlit as st
import pandas as pd
import numpy as np
import re
from pathlib import Path

def fix_streamlit_145_dataframe_display():
    """修复Streamlit 1.45.0版本的DataFrame显示问题"""
    st.title("🔧 Streamlit 1.45.0 DataFrame显示修复工具")
    
    st.info(f"当前Streamlit版本: {st.__version__}")
    
    # 检查版本兼容性
    if st.__version__.startswith('1.45') or st.__version__.startswith('1.4'):
        st.warning("⚠️ 检测到Streamlit 1.45.0版本，该版本对DataFrame显示有重要变化")
        
        st.markdown("""
        ### 🔄 版本1.45.0的主要变化：
        1. **默认容器宽度**：`st.dataframe` 现在默认使用 `use_container_width=True`
        2. **数据类型处理**：对DataFrame数据类型检查更加严格
        3. **显示逻辑优化**：内部渲染逻辑有所调整
        """)
    
    # 测试当前数据显示
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        st.subheader("📊 当前数据测试")
        
        # 测试1: 基本DataFrame显示
        st.write("**测试1: 基本st.dataframe()显示**")
        try:
            with st.container():
                st.dataframe(df.head(5))
            st.success("✅ 基本显示正常")
        except Exception as e:
            st.error(f"❌ 基本显示失败: {e}")
            
            # 尝试修复方案1: 明确设置容器宽度
            st.write("**尝试修复方案1: 明确设置use_container_width=False**")
            try:
                st.dataframe(df.head(5), use_container_width=False)
                st.success("✅ 修复方案1成功")
            except Exception as e2:
                st.error(f"❌ 修复方案1失败: {e2}")
        
        # 测试2: 使用st.table()
        st.write("**测试2: 使用st.table()显示**")
        try:
            st.table(df.head(3))
            st.success("✅ st.table()显示正常")
        except Exception as e:
            st.error(f"❌ st.table()显示失败: {e}")
        
        # 测试3: 使用st.write()
        st.write("**测试3: 使用st.write()显示**")
        try:
            st.write(df.head(3))
            st.success("✅ st.write()显示正常")
        except Exception as e:
            st.error(f"❌ st.write()显示失败: {e}")
        
        # 应用修复
        if st.button("🚀 应用Streamlit 1.45.0兼容性修复", type="primary"):
            apply_streamlit_145_fixes(df)
    
    else:
        st.error("❌ 没有找到当前数据，请先上传数据文件")
        
        # 创建测试数据
        if st.button("创建测试数据"):
            create_test_data_for_145()

def apply_streamlit_145_fixes(df):
    """应用针对Streamlit 1.45.0的修复"""
    st.subheader("🔧 应用修复...")
    
    try:
        # 修复1: 数据清理适配新版本
        st.write("1️⃣ 应用数据清理...")
        fixed_df = clean_dataframe_for_streamlit_145(df)
        
        # 修复2: 更新session_state
        st.write("2️⃣ 更新session_state...")
        st.session_state.current_data = fixed_df
        
        # 修复3: 测试修复后的显示
        st.write("3️⃣ 测试修复效果...")
        
        # 使用新版本推荐的显示方式
        with st.container():
            st.dataframe(
                fixed_df.head(10), 
                use_container_width=True,  # 明确设置，适配新版本默认行为
                hide_index=False
            )
        
        st.success("🎉 修复完成！DataFrame现在应该能正确显示了")
        
        # 显示修复摘要
        col1, col2 = st.columns(2)
        with col1:
            st.metric("原始数据行数", len(df))
            st.metric("原始数据列数", len(df.columns))
        
        with col2:
            st.metric("修复后行数", len(fixed_df))
            st.metric("修复后列数", len(fixed_df.columns))
        
        # 提供额外的显示选项
        st.subheader("📋 其他显示选项测试")
        
        display_option = st.selectbox(
            "选择显示方式",
            ["默认dataframe", "紧凑table", "HTML表格", "原始数据"]
        )
        
        if display_option == "默认dataframe":
            st.dataframe(fixed_df.head(10), use_container_width=True)
        elif display_option == "紧凑table":
            st.table(fixed_df.head(5))
        elif display_option == "HTML表格":
            st.markdown(fixed_df.head(5).to_html(escape=False), unsafe_allow_html=True)
        elif display_option == "原始数据":
            st.text(str(fixed_df.head(5)))
        
    except Exception as e:
        st.error(f"❌ 修复过程中出现错误: {e}")
        st.code(str(e))

def clean_dataframe_for_streamlit_145(df):
    """针对Streamlit 1.45.0优化DataFrame"""
    if df is None or df.empty:
        return df
    
    cleaned_df = df.copy()
    
    # 1. 处理新版本对数据类型的严格要求
    for col in cleaned_df.columns:
        # 确保列名是字符串
        if not isinstance(col, str):
            cleaned_df = cleaned_df.rename(columns={col: str(col)})
    
    # 重新获取列名（可能已更改）
    cleaned_df.columns = [str(col).strip() for col in cleaned_df.columns]
    
    # 2. 处理数值列的特殊值（新版本更严格）
    numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        # 处理无穷大值
        if np.isinf(cleaned_df[col]).any():
            cleaned_df[col] = cleaned_df[col].replace([np.inf, -np.inf], np.nan)
        
        # 处理NaN值
        if cleaned_df[col].isnull().any():
            # 使用更保守的填充策略
            if cleaned_df[col].notna().any():
                cleaned_df[col] = cleaned_df[col].fillna(cleaned_df[col].median())
            else:
                cleaned_df[col] = cleaned_df[col].fillna(0)
    
    # 3. 处理文本列
    text_cols = cleaned_df.select_dtypes(include=['object']).columns
    for col in text_cols:
        # 确保文本列不包含混合类型
        cleaned_df[col] = cleaned_df[col].astype(str)
        # 处理空值
        cleaned_df[col] = cleaned_df[col].replace('nan', '').replace('None', '')
    
    # 4. 重置索引（新版本对索引更敏感）
    if cleaned_df.index.duplicated().any():
        cleaned_df = cleaned_df.reset_index(drop=True)
    
    # 5. 确保DataFrame不为空
    if cleaned_df.empty:
        # 创建一个最小的有效DataFrame
        cleaned_df = pd.DataFrame({'提示': ['数据为空']})
    
    return cleaned_df

def create_test_data_for_145():
    """创建适合Streamlit 1.45.0测试的数据"""
    st.write("创建测试数据...")
    
    # 创建包含各种数据类型的测试数据
    test_data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25000.50, 20200.75, 35000.00, 15000.25, 12000.00],
        '销量': [100, 80, 60, 120, 90],
        '地区': ['北京', '上海', '广州', '深圳', '杭州'],
        '上市日期': pd.to_datetime(['2023-09-15', '2023-10-20', '2023-11-01', '2023-08-15', '2023-09-30']),
        '是否热销': [True, True, False, True, False]
    }
    
    test_df = pd.DataFrame(test_data)
    st.session_state.current_data = test_df
    
    st.success("✅ 测试数据已创建")
    st.dataframe(test_df, use_container_width=True)

def patch_result_formatter_for_145():
    """为result_formatter.py提供Streamlit 1.45.0兼容性补丁"""
    st.subheader("🔧 result_formatter.py 兼容性补丁")
    
    patch_code = '''
# 在result_formatter.py中添加以下兼容性代码

def display_dataframe_145_compatible(df, inline_mode=False):
    """Streamlit 1.45.0兼容的DataFrame显示方法"""
    try:
        # 确保DataFrame符合新版本要求
        if df is None or df.empty:
            st.info("📊 数据为空")
            return
        
        # 数据预处理
        display_df = df.copy()
        
        # 处理列名
        display_df.columns = [str(col).strip() for col in display_df.columns]
        
        # 处理数值列
        numeric_cols = display_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            display_df[col] = display_df[col].replace([np.inf, -np.inf], np.nan)
            display_df[col] = display_df[col].fillna(0)
        
        # 使用新版本推荐的显示方式
        with st.container():
            st.dataframe(
                display_df,
                use_container_width=True,  # 明确设置，适配新版本
                hide_index=len(display_df) > 10
            )
            
    except Exception as e:
        st.error(f"DataFrame显示失败: {e}")
        # 备用显示方案
        try:
            st.table(df.head(5))
        except:
            st.text(str(df))
'''
    
    st.code(patch_code, language='python')
    
    if st.button("📋 复制补丁代码"):
        st.success("请手动复制上述代码到您的result_formatter.py文件中")

def main():
    """主函数"""
    st.set_page_config(
        page_title="Streamlit 1.45.0 DataFrame修复",
        page_icon="🔧",
        layout="wide"
    )
    
    # 主修复功能
    fix_streamlit_145_dataframe_display()
    
    # 分隔线
    st.markdown("---")
    
    # 提供补丁代码
    patch_result_formatter_for_145()
    
    # 额外建议
    st.markdown("---")
    st.subheader("💡 额外建议")
    st.markdown("""
    ### 如果问题仍然存在，请尝试：
    
    1. **降级到稳定版本**：
       ```bash
       pip install streamlit==1.41.0
       ```
    
    2. **清除缓存**：
       ```bash
       streamlit cache clear
       ```
    
    3. **检查依赖兼容性**：
       ```bash
       pip install pandas==1.5.3 numpy==1.24.3
       ```
    
    4. **重启应用**：
       完全关闭并重新启动Streamlit应用
    """)

if __name__ == "__main__":
    main()
