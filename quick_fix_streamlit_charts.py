#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复Streamlit图表问题
一键解决销售额_start、销售额_end字段和无穷大值问题
"""

import pandas as pd
import numpy as np
import streamlit as st

def quick_fix_current_data():
    """快速修复当前数据"""
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        st.write("🔍 **检测到的问题:**")
        
        # 检测问题字段
        problematic_fields = []
        for col in df.columns:
            if any(pattern in str(col) for pattern in ['_start', '_end']) and '销售额' in str(col):
                problematic_fields.append(col)
        
        if problematic_fields:
            st.error(f"❌ 问题字段: {problematic_fields}")
        else:
            st.success("✅ 未发现问题字段")
        
        # 检测无穷大值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        infinite_cols = []
        for col in numeric_cols:
            if np.isinf(df[col]).any():
                infinite_cols.append(col)
        
        if infinite_cols:
            st.error(f"❌ 包含无穷大值的列: {infinite_cols}")
        else:
            st.success("✅ 未发现无穷大值")
        
        # 修复按钮
        if problematic_fields or infinite_cols:
            if st.button("🚨 立即修复", type="primary"):
                with st.spinner("正在修复..."):
                    # 移除问题字段
                    if problematic_fields:
                        df = df.drop(columns=problematic_fields)
                        st.success(f"✅ 已移除问题字段: {problematic_fields}")
                    
                    # 清理无穷大值
                    for col in numeric_cols:
                        if col in df.columns and np.isinf(df[col]).any():
                            df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                            df[col] = df[col].fillna(0)
                    
                    # 更新数据
                    st.session_state.current_data = df
                    
                    # 清理缓存
                    st.cache_data.clear()
                    st.cache_resource.clear()
                    
                    st.success("🎉 修复完成！")
                    st.info("💡 建议刷新页面以确保修复生效")
                    
                    if st.button("🔄 刷新页面"):
                        st.rerun()
        else:
            st.success("🎉 数据状态正常，无需修复")
    else:
        st.info("⚠️ 当前没有加载数据")

def main():
    """主函数"""
    st.title("⚡ 快速修复Streamlit图表问题")
    st.markdown("一键解决销售额_start、销售额_end字段和无穷大值导致的图表渲染问题")
    
    # 当前数据状态
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("数据行数", len(df))
        with col2:
            st.metric("数据列数", len(df.columns))
        with col3:
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            st.metric("数值列数", len(numeric_cols))
        
        # 快速修复
        quick_fix_current_data()
        
        # 数据预览
        with st.expander("📊 数据预览"):
            st.dataframe(df.head())
        
    else:
        st.warning("⚠️ 请先在主应用中上传数据文件")
        st.markdown("### 📖 使用说明")
        st.markdown("""
        1. 在主应用中上传数据文件
        2. 返回此页面进行快速修复
        3. 或使用完整的修复工具：`streamlit run streamlit_chart_fix.py --server.port=8505`
        """)
    
    # 工具链接
    st.markdown("---")
    st.markdown("### 🔧 其他修复工具")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🔧 打开完整修复工具"):
            st.markdown("请运行: `streamlit run streamlit_chart_fix.py --server.port=8505`")
    
    with col2:
        if st.button("🔍 打开运行时监控"):
            st.markdown("请运行: `streamlit run runtime_monitor.py --server.port=8506`")

if __name__ == "__main__":
    main()
