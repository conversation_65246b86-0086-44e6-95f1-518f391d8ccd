#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速DataFrame显示问题诊断
专门针对Streamlit 1.45.0版本问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys

def main():
    st.title("🔍 DataFrame显示问题快速诊断")
    
    # 显示版本信息
    st.subheader("📋 环境信息")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Streamlit版本", st.__version__)
    with col2:
        st.metric("Pandas版本", pd.__version__)
    with col3:
        st.metric("Python版本", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 版本兼容性检查
    if st.__version__.startswith('1.45') or st.__version__.startswith('1.44') or st.__version__.startswith('1.43'):
        st.warning("⚠️ 检测到Streamlit 1.43+版本，该版本对DataFrame显示有重要变化")
        st.info("💡 主要变化：`st.dataframe` 现在默认使用 `use_container_width=True`")
    
    # 创建测试数据
    st.subheader("🧪 DataFrame显示测试")
    
    # 简单测试数据
    simple_data = {
        '产品': ['iPhone', 'iPad', 'MacBook'],
        '价格': [999, 599, 1299],
        '销量': [100, 80, 60]
    }
    simple_df = pd.DataFrame(simple_data)
    
    st.write("**测试数据：**")
    st.write(simple_df)
    
    # 测试不同的显示方法
    st.subheader("🔬 显示方法测试")
    
    methods = {
        "st.dataframe() - 默认": lambda df: st.dataframe(df),
        "st.dataframe() - 明确设置宽度": lambda df: st.dataframe(df, use_container_width=True),
        "st.dataframe() - 不使用容器宽度": lambda df: st.dataframe(df, use_container_width=False),
        "st.table()": lambda df: st.table(df),
        "st.write()": lambda df: st.write(df)
    }
    
    for method_name, method_func in methods.items():
        st.write(f"**{method_name}:**")
        try:
            with st.container():
                method_func(simple_df)
            st.success(f"✅ {method_name} 显示成功")
        except Exception as e:
            st.error(f"❌ {method_name} 显示失败: {e}")
        
        st.markdown("---")
    
    # 测试复杂数据
    st.subheader("🔧 复杂数据测试")
    
    if st.button("测试包含特殊值的数据"):
        # 创建包含特殊值的数据
        complex_data = {
            '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
            '销售额': [25000.5, np.inf, 35000.0, np.nan],
            '销量': [100, 80, 60, 120],
            '地区': ['北京', '上海', None, '深圳']
        }
        complex_df = pd.DataFrame(complex_data)
        
        st.write("**原始复杂数据：**")
        st.write(complex_df)
        
        # 清理数据
        cleaned_df = complex_df.copy()
        cleaned_df['销售额'] = cleaned_df['销售额'].replace([np.inf, -np.inf], np.nan).fillna(0)
        cleaned_df['地区'] = cleaned_df['地区'].fillna('未知')
        
        st.write("**清理后的数据：**")
        try:
            st.dataframe(cleaned_df, use_container_width=True)
            st.success("✅ 复杂数据显示成功")
        except Exception as e:
            st.error(f"❌ 复杂数据显示失败: {e}")
    
    # 检查当前应用数据
    st.subheader("📊 当前应用数据检查")
    
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        st.write(f"**数据形状：** {df.shape}")
        st.write(f"**列名：** {list(df.columns)}")
        st.write(f"**数据类型：**")
        st.write(df.dtypes)
        
        # 检查问题
        issues = []
        
        # 检查无穷大值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            inf_count = np.isinf(df[col]).sum()
            if inf_count > 0:
                issues.append(f"列 '{col}' 包含 {inf_count} 个无穷大值")
        
        # 检查NaN值
        for col in df.columns:
            nan_count = df[col].isnull().sum()
            if nan_count > len(df) * 0.5:
                issues.append(f"列 '{col}' 包含过多空值: {nan_count}/{len(df)}")
        
        # 检查列名问题
        for col in df.columns:
            if not isinstance(col, str):
                issues.append(f"列名 '{col}' 不是字符串类型")
        
        if issues:
            st.warning("⚠️ 发现以下问题：")
            for issue in issues:
                st.write(f"- {issue}")
        else:
            st.success("✅ 数据检查通过")
        
        # 尝试显示当前数据
        st.write("**尝试显示当前数据：**")
        try:
            st.dataframe(df.head(5), use_container_width=True)
            st.success("✅ 当前数据显示正常")
        except Exception as e:
            st.error(f"❌ 当前数据显示失败: {e}")
            
            # 尝试修复
            if st.button("🔧 尝试修复当前数据"):
                try:
                    fixed_df = df.copy()
                    
                    # 修复列名
                    fixed_df.columns = [str(col).strip() for col in fixed_df.columns]
                    
                    # 修复数值列
                    numeric_cols = fixed_df.select_dtypes(include=[np.number]).columns
                    for col in numeric_cols:
                        fixed_df[col] = fixed_df[col].replace([np.inf, -np.inf], np.nan).fillna(0)
                    
                    # 修复文本列
                    text_cols = fixed_df.select_dtypes(include=['object']).columns
                    for col in text_cols:
                        fixed_df[col] = fixed_df[col].astype(str).replace('nan', '').replace('None', '')
                    
                    # 显示修复后的数据
                    st.dataframe(fixed_df.head(5), use_container_width=True)
                    st.success("🎉 数据修复成功！")
                    
                    if st.button("✅ 应用修复到当前数据"):
                        st.session_state.current_data = fixed_df
                        st.success("修复已应用！请刷新页面查看效果。")
                        st.rerun()
                        
                except Exception as fix_error:
                    st.error(f"❌ 修复失败: {fix_error}")
    
    else:
        st.info("没有找到当前应用数据")
    
    # 提供解决方案建议
    st.subheader("💡 解决方案建议")
    
    st.markdown("""
    ### 如果DataFrame不能正确显示，请尝试：
    
    1. **明确设置容器宽度**：
       ```python
       st.dataframe(df, use_container_width=True)
       ```
    
    2. **使用st.table()作为备用**：
       ```python
       st.table(df.head(10))
       ```
    
    3. **清理数据中的特殊值**：
       ```python
       df = df.replace([np.inf, -np.inf], np.nan).fillna(0)
       ```
    
    4. **确保列名是字符串**：
       ```python
       df.columns = [str(col) for col in df.columns]
       ```
    
    5. **如果问题持续，考虑降级Streamlit版本**：
       ```bash
       pip install streamlit==1.41.0
       ```
    """)

if __name__ == "__main__":
    main()
