#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DataFrame显示修复效果
验证修改后的提示词是否能正确生成Streamlit显示代码
"""

import streamlit as st
import pandas as pd
import numpy as np
from perfect_tongyi_integration import TongyiQianwenLLM

def main():
    st.title("🧪 DataFrame显示修复测试")
    
    # 创建测试数据
    st.subheader("📊 测试数据")
    test_data = {
        '销售员': ['张三', '李四', '王五', '赵六', '钱七'],
        '销售额': [25000, 32000, 18000, 45000, 28000],
        '销量': [100, 120, 80, 150, 110],
        '地区': ['北京', '上海', '广州', '深圳', '杭州']
    }
    
    df = pd.DataFrame(test_data)
    st.dataframe(df, use_container_width=True)
    
    # 测试不同类型的查询
    st.subheader("🔬 测试查询")
    
    test_queries = [
        "按销售员分组，计算总销售额",
        "哪个销售员的销售额最高？",
        "计算各地区的平均销售额",
        "显示销售额排名前3的销售员"
    ]
    
    # 创建LLM实例
    try:
        llm = TongyiQianwenLLM()
        st.success("✅ LLM初始化成功")
    except Exception as e:
        st.error(f"❌ LLM初始化失败: {e}")
        return
    
    # 测试每个查询
    for i, query in enumerate(test_queries, 1):
        st.markdown(f"### 测试 {i}: {query}")
        
        if st.button(f"执行测试 {i}", key=f"test_{i}"):
            with st.spinner(f"正在生成代码..."):
                try:
                    # 生成代码
                    code = llm.call(query, df.to_string())
                    
                    st.write("**生成的代码:**")
                    st.code(code, language='python')
                    
                    # 检查代码是否使用了Streamlit组件
                    uses_streamlit = any(method in code for method in [
                        'st.dataframe', 'st.metric', 'st.write', 'st.success', 'st.info'
                    ])
                    
                    uses_print = 'print(' in code
                    
                    if uses_streamlit and not uses_print:
                        st.success("✅ 代码正确使用了Streamlit组件，没有使用print()")
                    elif uses_streamlit and uses_print:
                        st.warning("⚠️ 代码同时使用了Streamlit组件和print()，建议优化")
                    elif uses_print and not uses_streamlit:
                        st.error("❌ 代码仍然使用print()，没有使用Streamlit组件")
                    else:
                        st.info("ℹ️ 代码没有明显的输出语句")
                    
                    # 执行代码测试
                    st.write("**执行结果:**")
                    
                    # 创建安全的执行环境
                    exec_globals = {
                        'df': df.copy(),
                        'pd': pd,
                        'np': np,
                        'st': st
                    }
                    
                    try:
                        exec(code, exec_globals)
                        st.success("✅ 代码执行成功")
                    except Exception as exec_error:
                        st.error(f"❌ 代码执行失败: {exec_error}")
                        st.code(str(exec_error))
                
                except Exception as e:
                    st.error(f"❌ 代码生成失败: {e}")
        
        st.markdown("---")
    
    # 对比测试
    st.subheader("📋 修复前后对比")
    
    st.markdown("""
    ### 🔧 修复内容总结
    
    **修复前的问题:**
    - 提示词要求使用 `print()` 输出结果
    - DataFrame显示为纯文本，无法正确渲染
    - 用户体验差，数据不易阅读
    
    **修复后的改进:**
    - 要求使用 `st.dataframe()` 显示DataFrame
    - 使用 `st.metric()` 显示数值结果
    - 使用 `st.success()` 和 `st.info()` 显示结论
    - DataFrame正确渲染为交互式表格
    
    **预期效果:**
    - ✅ DataFrame显示为交互式表格
    - ✅ 数值结果使用指标卡显示
    - ✅ 结论使用彩色提示框显示
    - ✅ 整体用户体验大幅提升
    """)
    
    # 手动测试区域
    st.subheader("🎯 手动测试区域")
    
    custom_query = st.text_input("输入自定义查询:", placeholder="例如：计算总销售额")
    
    if st.button("执行自定义查询") and custom_query:
        with st.spinner("正在处理..."):
            try:
                code = llm.call(custom_query, df.to_string())
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**生成的代码:**")
                    st.code(code, language='python')
                
                with col2:
                    st.write("**代码分析:**")
                    
                    # 分析代码特征
                    features = []
                    if 'st.dataframe' in code:
                        features.append("✅ 使用st.dataframe显示表格")
                    if 'st.metric' in code:
                        features.append("✅ 使用st.metric显示指标")
                    if 'st.write' in code:
                        features.append("✅ 使用st.write显示内容")
                    if 'st.success' in code or 'st.info' in code:
                        features.append("✅ 使用状态提示框")
                    if 'print(' in code:
                        features.append("⚠️ 仍包含print语句")
                    
                    for feature in features:
                        st.write(feature)
                
                # 执行代码
                st.write("**执行结果:**")
                exec_globals = {
                    'df': df.copy(),
                    'pd': pd,
                    'np': np,
                    'st': st
                }
                
                exec(code, exec_globals)
                
            except Exception as e:
                st.error(f"处理失败: {e}")

def create_comparison_demo():
    """创建修复前后对比演示"""
    st.subheader("🔄 修复前后对比演示")
    
    # 模拟修复前的代码
    old_code = """
# 修复前的代码（使用print）
grouped_data = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)
print(grouped_data)
print(f"销售额最高的是: {grouped_data.index[0]}, 销售额: {grouped_data.iloc[0]}")
"""
    
    # 修复后的代码
    new_code = """
# 修复后的代码（使用Streamlit组件）
grouped_data = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)
st.write("**各销售员销售额统计:**")
st.dataframe(grouped_data.to_frame('销售额'), use_container_width=True)
st.success(f"销售额最高的是: {grouped_data.index[0]}, 销售额: {grouped_data.iloc[0]:,}")
"""
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**修复前 (使用print):**")
        st.code(old_code, language='python')
        st.error("❌ 显示为纯文本，难以阅读")
    
    with col2:
        st.write("**修复后 (使用Streamlit组件):**")
        st.code(new_code, language='python')
        st.success("✅ 显示为交互式表格，用户体验佳")

if __name__ == "__main__":
    main()
    create_comparison_demo()
