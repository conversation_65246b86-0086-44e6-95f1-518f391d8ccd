#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表修复工具启动脚本
快速启动Streamlit图表问题修复工具
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    required_packages = ['streamlit', 'pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {missing_packages}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def run_chart_fix_tool():
    """运行图表修复工具"""
    print("🔧 启动Streamlit图表修复工具...")
    
    # 检查文件是否存在
    fix_tool_path = Path("streamlit_chart_fix.py")
    if not fix_tool_path.exists():
        print("❌ 找不到 streamlit_chart_fix.py 文件")
        return False
    
    try:
        # 启动Streamlit应用
        cmd = [sys.executable, "-m", "streamlit", "run", "streamlit_chart_fix.py", "--server.port=8505"]
        print(f"执行命令: {' '.join(cmd)}")
        
        subprocess.run(cmd, check=True)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return True

def run_runtime_monitor():
    """运行运行时监控工具"""
    print("🔍 启动运行时监控工具...")
    
    # 检查文件是否存在
    monitor_path = Path("runtime_monitor.py")
    if not monitor_path.exists():
        print("❌ 找不到 runtime_monitor.py 文件")
        return False
    
    try:
        # 启动Streamlit应用
        cmd = [sys.executable, "-m", "streamlit", "run", "runtime_monitor.py", "--server.port=8506"]
        print(f"执行命令: {' '.join(cmd)}")
        
        subprocess.run(cmd, check=True)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return True

def emergency_fix():
    """紧急修复"""
    print("🚨 执行紧急修复...")
    
    try:
        from data_source_cleaner import fix_chart_rendering_issues, emergency_data_fix
        
        # 执行紧急修复
        success1 = emergency_data_fix()
        success2 = fix_chart_rendering_issues()
        
        if success1 or success2:
            print("✅ 紧急修复完成！")
            print("💡 建议重启主应用以确保修复生效")
        else:
            print("❌ 紧急修复失败")
            
    except ImportError as e:
        print(f"❌ 导入修复模块失败: {e}")
    except Exception as e:
        print(f"❌ 执行修复失败: {e}")

def main():
    """主函数"""
    print("🔧 Streamlit图表问题修复工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    print("\n请选择操作:")
    print("1. 启动图表修复工具 (端口8505)")
    print("2. 启动运行时监控工具 (端口8506)")
    print("3. 执行紧急修复 (命令行)")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == "1":
                run_chart_fix_tool()
                break
            elif choice == "2":
                run_runtime_monitor()
                break
            elif choice == "3":
                emergency_fix()
                break
            elif choice == "4":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
