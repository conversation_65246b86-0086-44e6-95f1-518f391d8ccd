#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试图表修复功能
验证所有修复工具的有效性
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

def create_problematic_data():
    """创建包含问题的测试数据"""
    print("🧪 创建包含问题的测试数据...")
    
    # 正常数据
    data = {
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [10000, 15000, 20000],
        '销量': [5, 3, 2]
    }
    
    # 添加问题字段
    data['销售额_start'] = [10000, np.inf, 20000]  # 包含正无穷
    data['销售额_end'] = [15000, 18000, -np.inf]   # 包含负无穷
    
    df = pd.DataFrame(data)
    
    print("✅ 测试数据创建完成")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 检查问题
    problematic_fields = [col for col in df.columns if '_start' in str(col) or '_end' in str(col)]
    print(f"问题字段: {problematic_fields}")
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        inf_count = np.isinf(df[col]).sum()
        if inf_count > 0:
            print(f"无穷大值: {col} ({inf_count}个)")
    
    return df

def test_chart_fixer():
    """测试图表修复器"""
    print("\n🔧 测试图表修复器...")
    
    try:
        from streamlit_chart_fix import StreamlitChartFixer
        
        # 创建问题数据
        df = create_problematic_data()
        
        # 检测问题字段
        problematic_fields = StreamlitChartFixer.detect_problematic_fields(df)
        print(f"检测到问题字段: {problematic_fields}")
        
        # 检测无穷大值
        infinite_columns = StreamlitChartFixer.detect_infinite_values(df)
        print(f"检测到无穷大值列: {list(infinite_columns.keys())}")
        
        # 清理数据
        cleaned_df = StreamlitChartFixer.clean_dataframe(df)
        
        print("\n✅ 修复后数据状态:")
        print(f"数据形状: {cleaned_df.shape}")
        print(f"列名: {list(cleaned_df.columns)}")
        
        # 验证修复效果
        remaining_problematic = StreamlitChartFixer.detect_problematic_fields(cleaned_df)
        remaining_infinite = StreamlitChartFixer.detect_infinite_values(cleaned_df)
        
        if not remaining_problematic and not remaining_infinite:
            print("🎉 修复成功！所有问题已解决")
            return True
        else:
            print(f"❌ 修复不完整: 剩余问题字段={remaining_problematic}, 剩余无穷大值={remaining_infinite}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入修复器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_data_source_cleaner():
    """测试数据源清理器"""
    print("\n🧹 测试数据源清理器...")
    
    try:
        from data_source_cleaner import DataSourceCleaner, validate_data_for_charts
        
        # 创建问题数据
        df = create_problematic_data()
        
        # 验证问题
        issues = validate_data_for_charts(df)
        print(f"发现问题: {issues}")
        
        # 清理数据
        cleaned_df = DataSourceCleaner.clean_dataframe_at_source(df)
        
        print("\n✅ 清理后数据状态:")
        print(f"数据形状: {cleaned_df.shape}")
        print(f"列名: {list(cleaned_df.columns)}")
        
        # 验证清理效果
        remaining_issues = validate_data_for_charts(cleaned_df)
        
        if not remaining_issues:
            print("🎉 清理成功！所有问题已解决")
            return True
        else:
            print(f"❌ 清理不完整: 剩余问题={remaining_issues}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入清理器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_chart_rendering():
    """测试图表渲染"""
    print("\n📊 测试图表渲染...")
    
    try:
        # 创建清理后的数据
        df = create_problematic_data()
        
        # 使用修复器清理
        from streamlit_chart_fix import StreamlitChartFixer
        cleaned_df = StreamlitChartFixer.clean_dataframe(df)
        
        # 尝试创建图表数据
        if '产品名称' in cleaned_df.columns and '销售额' in cleaned_df.columns:
            chart_data = cleaned_df.groupby('产品名称')['销售额'].sum()
            
            print("图表数据:")
            print(chart_data)
            
            # 验证图表数据
            is_valid, message = StreamlitChartFixer.validate_chart_data(chart_data)
            
            if is_valid:
                print("✅ 图表数据验证通过")
                return True
            else:
                print(f"❌ 图表数据验证失败: {message}")
                return False
        else:
            print("❌ 缺少必要的列用于图表渲染")
            return False
            
    except Exception as e:
        print(f"❌ 图表渲染测试失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始综合测试...")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 图表修复器
    result1 = test_chart_fixer()
    test_results.append(("图表修复器", result1))
    
    # 测试2: 数据源清理器
    result2 = test_data_source_cleaner()
    test_results.append(("数据源清理器", result2))
    
    # 测试3: 图表渲染
    result3 = test_chart_rendering()
    test_results.append(("图表渲染", result3))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复工具工作正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查修复工具")
        return False

def main():
    """主函数"""
    print("🔧 Streamlit图表修复功能综合测试")
    print("=" * 50)
    
    # 检查依赖
    required_modules = ['pandas', 'numpy']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 可用")
        except ImportError:
            print(f"❌ {module} 不可用")
            return
    
    # 运行测试
    success = run_comprehensive_test()
    
    if success:
        print("\n💡 建议:")
        print("1. 修复工具已准备就绪")
        print("2. 可以在实际应用中使用")
        print("3. 遇到图表问题时立即运行修复")
        print("\n🚀 下一步:")
        print("1. 运行: python run_chart_fix.py")
        print("2. 或直接启动: streamlit run streamlit_chart_fix.py --server.port=8505")
    else:
        print("\n⚠️ 注意:")
        print("1. 部分功能可能存在问题")
        print("2. 请检查相关模块的导入")
        print("3. 确保所有依赖已正确安装")

if __name__ == "__main__":
    main()
