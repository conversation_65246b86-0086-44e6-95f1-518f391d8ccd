#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的结果格式化模块
基于PandasAI输出分析，提供最佳的Streamlit显示体验
"""

import streamlit as st
import pandas as pd
import re
from io import StringIO
import numpy as np
from pathlib import Path

class EnhancedResultFormatter:
    """增强的结果格式化器"""

    @staticmethod
    def format_and_display_result(result, inline_mode=False):
        """格式化并直接显示结果 - 主入口函数

        Args:
            result: 分析结果字典
            inline_mode: 是否为内联模式（聊天消息中显示）
        """
        if not result or not result.get('success'):
            if not inline_mode:
                st.error("❌ 分析未成功完成")
            return

        output = result.get('output', '')
        if not output:
            if not inline_mode:
                st.info("ℹ️ 分析完成，但没有输出内容")
            return

        # 检测输出类型并选择最佳显示方式
        output_type = EnhancedResultFormatter._detect_output_type(output)

        # 根据类型选择显示方法
        if output_type == 'dataframe_info':
            EnhancedResultFormatter._display_dataframe_info(output, result, inline_mode)
        elif output_type == 'statistics_summary':
            EnhancedResultFormatter._display_statistics_summary(output, result, inline_mode)
        elif output_type == 'single_number':
            EnhancedResultFormatter._display_single_number(output, result, inline_mode)
        elif output_type == 'tabular_data':
            EnhancedResultFormatter._display_tabular_data(output, result, inline_mode)
        elif output_type == 'series_data':
            EnhancedResultFormatter._display_series_data(output, result, inline_mode)
        elif output_type == 'mixed_data_with_answer':
            EnhancedResultFormatter._display_mixed_data_with_answer(output, result, inline_mode)
        elif output_type == 'correlation_matrix':
            EnhancedResultFormatter._display_correlation_matrix(output, result, inline_mode)
        else:
            # 默认文本显示，但格式化
            EnhancedResultFormatter._display_formatted_text(output, result, inline_mode)

        # 显示用户生成的图表
        if result.get('has_chart'):
            if result.get('uses_plotly_native'):
                # Plotly原生图表需要在Streamlit上下文中重新执行
                EnhancedResultFormatter._display_plotly_chart(result, inline_mode)
            else:
                # 其他图表（如matplotlib）
                EnhancedResultFormatter._display_user_chart(result, inline_mode)

    @staticmethod
    def _detect_output_type(output):
        """检测输出类型"""
        lines = output.strip().split('\n')

        # DataFrame信息检测 - 增强检测
        dataframe_keywords = ['DataFrame', 'RangeIndex', 'Data columns', 'entries', 'Non-Null Count', 'Dtype', 'dtypes:', 'memory usage']
        if any(keyword in output for keyword in dataframe_keywords):
            # 进一步检查是否包含列信息格式
            if 'entries' in output or 'Non-Null Count' in output or 'Data columns' in output:
                return 'dataframe_info'

        # 统计摘要检测 (describe输出)
        if any(word in output for word in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']):
            # 进一步检查是否是标准的describe输出格式
            if len([line for line in lines if any(stat in line for stat in ['count', 'mean', 'std'])]) >= 2:
                return 'statistics_summary'

        # 相关性矩阵检测
        if '价格' in output and '销量' in output and any(word in output for word in ['1.000000', '-0.', '0.']):
            # 检查是否有矩阵格式
            matrix_lines = [line for line in lines if line.strip() and any(char.isdigit() for char in line)]
            if len(matrix_lines) >= 3:  # 至少3行数据
                return 'correlation_matrix'

        # 单一数值检测
        if len(lines) == 1 and lines[0].strip():
            try:
                float(lines[0].strip())
                return 'single_number'
            except:
                pass

        # 混合输出检测（包含表格数据和答案）
        has_tabular = False
        has_answer = False

        for line in lines:
            # 检查是否包含表格数据
            if re.match(r'^\d+\s+\S+\s+[\d.-]+', line.strip()):
                has_tabular = True
            # 检查是否包含明确答案
            if any(keyword in line for keyword in ['答案:', '最高', '最低', '最大', '最小']):
                has_answer = True

        if has_tabular and has_answer:
            return 'mixed_data_with_answer'
        elif has_tabular:
            return 'series_data'

        # 序列数据检测 (如 groupby 结果) - 增强检测
        # 检查是否是DataFrame输出格式（带索引的表格）
        dataframe_output_pattern = False
        if len(lines) >= 3:
            # 检查第一行是否是列名
            first_line = lines[0].strip()
            if any(col in first_line for col in ['产品名称', '地区', '类别', '销售员', '销售区域']) and any(col in first_line for col in ['销售额', '销量', '数量', '金额']):
                # 检查后续行是否是 "索引 值1 值2 ..." 格式（支持多列）
                data_lines = 0
                for line in lines[1:]:
                    line = line.strip()
                    # 修改正则表达式以支持多列DataFrame格式
                    if line and re.match(r'^\d+\s+\S+.*[\d.-]+', line):
                        data_lines += 1

                if data_lines >= 2:
                    dataframe_output_pattern = True

        # 传统序列数据检测
        series_pattern_count = 0
        for line in lines:
            if re.match(r'^[^\d\s]+\s+[\d.-]+$', line.strip()):
                series_pattern_count += 1

        if dataframe_output_pattern or series_pattern_count >= 2:
            return 'series_data'

        # 表格数据检测 - 降低优先级，避免与series_data冲突
        if len(lines) >= 3 and not dataframe_output_pattern:
            # 检查是否有表头和数据行
            potential_header = lines[0].strip()
            if len(potential_header.split()) >= 2:
                # 检查后续行是否有相似结构
                similar_structure_count = 0
                header_parts = len(potential_header.split())

                for line in lines[1:min(5, len(lines))]:
                    line_parts = len(line.strip().split())
                    if abs(line_parts - header_parts) <= 1:
                        similar_structure_count += 1

                if similar_structure_count >= 2:
                    return 'tabular_data'

        return 'text'

    @staticmethod
    def _display_dataframe_info(output, result, inline_mode=False):
        """显示DataFrame信息 - 支持混合输出（describe + info）"""
        if not inline_mode:
            st.subheader("📊 数据集概览")

        # 分离describe和info输出
        lines = output.split('\n')

        # 查找describe输出部分
        describe_start = -1
        describe_end = -1
        info_start = -1
        info_end = -1

        for i, line in enumerate(lines):
            if 'count' in line and 'mean' in line:
                describe_start = i - 1 if i > 0 else i
            elif describe_start != -1 and describe_end == -1 and line.strip() == '':
                describe_end = i
            elif 'DataFrame' in line and 'RangeIndex' in line:
                info_start = i
            elif info_start != -1 and info_end == -1 and ('memory usage' in line or 'dtypes:' in line):
                # 找到info结束位置
                for j in range(i, min(i + 5, len(lines))):
                    if 'memory usage' in lines[j]:
                        info_end = j + 1
                        break

        # 显示describe结果（如果存在）
        if describe_start != -1 and describe_end != -1:
            describe_output = '\n'.join(lines[describe_start:describe_end])
            if describe_output.strip():
                if not inline_mode:
                    st.subheader("📈 统计摘要")
                try:
                    # 尝试解析为表格
                    from io import StringIO
                    df_stats = pd.read_csv(StringIO(describe_output), sep='\s+', index_col=0)
                    st.dataframe(df_stats, use_container_width=True)
                except:
                    # 如果解析失败，显示原始文本
                    st.code(describe_output, language='text')

        # 显示info结果
        info_data = {}
        column_info = []

        if info_start != -1:
            info_lines = lines[info_start:info_end] if info_end != -1 else lines[info_start:]

            # 解析基本信息
            for line in info_lines:
                if 'entries' in line:
                    match = re.search(r'(\d+) entries', line)
                    if match:
                        info_data['rows'] = int(match.group(1))
                elif 'Data columns' in line:
                    match = re.search(r'(\d+) columns', line)
                    if match:
                        info_data['columns'] = int(match.group(1))
                elif 'memory usage' in line:
                    match = re.search(r'memory usage: ([\d.]+\+?\s*\w+)', line)
                    if match:
                        info_data['memory'] = match.group(1)

            # 解析列信息
            in_column_section = False
            for line in info_lines:
                if 'Column' in line and 'Non-Null Count' in line and 'Dtype' in line:
                    in_column_section = True
                    continue
                elif in_column_section and line.strip() and not line.startswith('dtypes:'):
                    # 解析列信息行
                    parts = line.strip().split()
                    if len(parts) >= 4:
                        try:
                            col_index = parts[0]
                            col_name = parts[1]
                            non_null_count = parts[2]
                            dtype = parts[-1]
                            column_info.append({
                                'Index': col_index,
                                'Column': col_name,
                                'Non-Null Count': non_null_count,
                                'Dtype': dtype
                            })
                        except:
                            pass
                elif in_column_section and (line.startswith('dtypes:') or not line.strip()):
                    break

        # 显示基本信息指标
        if info_data:
            if not inline_mode:
                st.subheader("📊 数据基本信息")
            col1, col2, col3 = st.columns(3)

            with col1:
                if 'rows' in info_data:
                    st.metric("📏 数据行数", f"{info_data['rows']:,}")

            with col2:
                if 'columns' in info_data:
                    st.metric("📋 数据列数", info_data['columns'])

            with col3:
                if 'memory' in info_data:
                    st.metric("💾 内存使用", info_data['memory'])

        # 显示列信息表格
        if column_info:
            if not inline_mode:
                st.subheader("📋 列信息详情")
            df_columns = pd.DataFrame(column_info)
            st.dataframe(df_columns, use_container_width=True, hide_index=True)

        # 详细信息可展开查看（仅在非内联模式下显示）
        if not inline_mode:
            with st.expander("🔍 完整输出", expanded=False):
                st.code(output, language='text')

    @staticmethod
    def _display_statistics_summary(output, result, inline_mode=False):
        """显示统计摘要"""
        if not inline_mode:
            st.subheader("📈 统计摘要")

        try:
            # 尝试解析为DataFrame
            lines = output.strip().split('\n')

            # 找到数据开始的行
            data_start = 0
            for i, line in enumerate(lines):
                if any(stat in line for stat in ['count', 'mean', 'std']):
                    data_start = i
                    break

            # 提取数据行
            data_lines = lines[data_start:]

            # 解析统计数据
            stats_data = {}
            for line in data_lines:
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0] in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']:
                    stat_name = parts[0]
                    values = [float(x) for x in parts[1:] if x.replace('.', '').replace('-', '').isdigit()]
                    if values:
                        stats_data[stat_name] = values

            if stats_data:
                # 创建DataFrame显示
                df_stats = pd.DataFrame(stats_data).T
                st.dataframe(df_stats, use_container_width=True)
            else:
                # 如果解析失败，显示原始文本
                st.text(output)

        except Exception:
            # 解析失败时显示原始文本
            st.text(output)

    @staticmethod
    def _display_single_number(output, result, inline_mode=False):
        """显示单一数值结果"""
        try:
            value = float(output.strip())

            # 根据查询内容推断数值类型
            query = result.get('query', '').lower()

            if any(word in query for word in ['总', '和', 'sum', '合计']):
                label = "📊 总计"
            elif any(word in query for word in ['平均', 'mean', '均值']):
                label = "📊 平均值"
            elif any(word in query for word in ['最大', 'max', '最高']):
                label = "📊 最大值"
            elif any(word in query for word in ['最小', 'min', '最低']):
                label = "📊 最小值"
            elif any(word in query for word in ['相关', 'corr', '关联']):
                label = "📊 相关系数"
            else:
                label = "📊 计算结果"

            # 使用大数字显示
            st.metric(label, f"{value:,.4f}".rstrip('0').rstrip('.'))

            # 添加解释
            if 'corr' in query or '相关' in query:
                if abs(value) > 0.7:
                    st.success("💡 强相关关系")
                elif abs(value) > 0.3:
                    st.info("💡 中等相关关系")
                else:
                    st.warning("💡 弱相关关系")

        except ValueError:
            st.text(output)

    @staticmethod
    def _display_tabular_data(output, result, inline_mode=False):
        """显示表格数据 - 改进版本"""
        if not inline_mode:
            st.subheader("📋 数据表格")

        try:
            # 首先检查是否是直接的DataFrame输出
            if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
                # 如果输出看起来像是DataFrame的字符串表示，尝试直接使用原始数据
                if 'DataFrame' in str(type(result.get('raw_output', ''))):
                    raw_df = result.get('raw_output')
                    if isinstance(raw_df, pd.DataFrame):
                        EnhancedResultFormatter._display_clean_dataframe(raw_df, inline_mode)
                        return

            # 尝试解析文本输出为DataFrame
            lines = output.strip().split('\n')

            # 改进的表头检测
            header_line = None
            data_start = 0

            # 寻找包含列名的行
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.isdigit():
                    # 检查是否包含常见的列名模式
                    if any(keyword in line.lower() for keyword in ['name', '名称', 'product', '产品', 'region', '地区', 'amount', '金额', 'sales', '销售']):
                        header_line = line
                        data_start = i + 1
                        break
                    # 或者检查是否是分隔符分隔的多列数据
                    parts = re.split(r'\s{2,}|\t', line)  # 使用多个空格或制表符分割
                    if len(parts) >= 2:
                        header_line = line
                        data_start = i + 1
                        break

            if header_line and data_start < len(lines):
                # 使用改进的解析方法
                headers = re.split(r'\s{2,}|\t', header_line.strip())

                # 解析数据行
                data_rows = []
                for line in lines[data_start:]:
                    line = line.strip()
                    if line:
                        # 使用相同的分割方法
                        parts = re.split(r'\s{2,}|\t', line)
                        if len(parts) >= len(headers):
                            data_rows.append(parts[:len(headers)])

                if data_rows:
                    # 创建DataFrame
                    df = pd.DataFrame(data_rows, columns=headers)

                    # 智能数据类型转换
                    EnhancedResultFormatter._smart_convert_columns(df)

                    # 显示DataFrame
                    EnhancedResultFormatter._display_clean_dataframe(df, inline_mode)
                else:
                    # 如果解析失败，显示格式化的文本
                    EnhancedResultFormatter._display_formatted_text_table(output, inline_mode)
            else:
                # 如果没有找到表头，显示格式化的文本
                EnhancedResultFormatter._display_formatted_text_table(output, inline_mode)

        except Exception as e:
            # 错误处理 - 显示原始输出和错误信息
            if not inline_mode:
                st.error(f"表格解析失败: {e}")
            EnhancedResultFormatter._display_formatted_text_table(output, inline_mode)

    @staticmethod
    def _display_series_data(output, result, inline_mode=False):
        """显示序列数据"""
        if not inline_mode:
            st.subheader("📊 数据序列")

        try:
            lines = output.strip().split('\n')
            data_dict = {}
            full_data_rows = []  # 保存完整的多列数据

            # 解析序列数据 - 改进版本，正确处理DataFrame格式
            header_line = None
            header_columns = []

            for i, line in enumerate(lines):
                line = line.strip()

                # 过滤掉调试输出和无关信息（修复调试输出被当作产品名称的问题）
                debug_patterns = [
                    '数据范围:', '数据类型:', '数据形状:', '是否包含',
                    'print(', 'dtype:', 'Name:', '图表数据:', '使用列:',
                    '数据验证:', '清理后数据:', '原始数据:', '测试数据:',
                    '列名映射:', '修复检查:', '执行成功', '生成的代码'
                ]

                # 跳过调试输出行
                if any(pattern in line for pattern in debug_patterns):
                    continue

                if line and not line.startswith('Name:') and not line.startswith('dtype:'):
                    # 检测并保存表头行
                    if i == 0 and any(header in line for header in ['产品名称', '地区', '类别', '销售员', '销售区域']):
                        header_line = line
                        header_columns = line.split()
                        continue

                    # 检查是否是DataFrame格式（索引 + 多列数据）
                    parts = line.split()
                    if len(parts) >= 3 and parts[0].isdigit():
                        # DataFrame格式：索引 + 多列数据
                        try:
                            # 动态解析多列数据
                            if header_columns and len(parts) >= len(header_columns):
                                # 根据表头确定列的含义
                                row_data = {}
                                for j, col_name in enumerate(header_columns):
                                    if j + 1 < len(parts):  # +1 因为要跳过索引列
                                        row_data[col_name] = parts[j + 1]

                                # 保存完整行数据
                                full_data_rows.append(row_data)

                                # 寻找关键列（用作key和value）- 保持向后兼容
                                key_col = None
                                value_col = None

                                # 确定key列（区域、产品名称等）
                                for col in ['销售区域', '产品名称', '地区', '类别', '销售员']:
                                    if col in row_data:
                                        key_col = col
                                        break

                                # 确定value列（销售额、销量等数值）
                                for col in ['销售金额', '销售额', '销量', '数量', '金额']:
                                    if col in row_data:
                                        value_col = col
                                        break

                                # 如果找到了key和value列，添加到数据字典（向后兼容）
                                if key_col and value_col:
                                    try:
                                        key = row_data[key_col]
                                        # 安全转换数值，处理无穷大值（修复Vega-Lite警告）
                                        raw_value = float(row_data[value_col])
                                        if np.isinf(raw_value) or np.isnan(raw_value):
                                            value = 0.0  # 将异常值转换为0
                                        else:
                                            value = raw_value
                                        data_dict[key] = value
                                    except (ValueError, KeyError):
                                        continue
                            else:
                                # 回退到原有逻辑：假设最后一列是数值
                                key = parts[1]  # 第二列作为key
                                # 安全转换数值，处理无穷大值（修复Vega-Lite警告）
                                raw_value = float(parts[-1])
                                if np.isinf(raw_value) or np.isnan(raw_value):
                                    value = 0.0  # 将异常值转换为0
                                else:
                                    value = raw_value
                                data_dict[key] = value
                        except (ValueError, IndexError):
                            continue
                    else:
                        # 传统序列格式：产品名称 销售额
                        parts = line.rsplit(None, 1)  # 从右边分割
                        if len(parts) == 2:
                            try:
                                key = parts[0]
                                # 再次检查是否是调试输出
                                if any(pattern in key for pattern in debug_patterns):
                                    continue
                                # 安全转换数值，处理无穷大值（修复Vega-Lite警告）
                                raw_value = float(parts[1])
                                if np.isinf(raw_value) or np.isnan(raw_value):
                                    value = 0.0  # 将异常值转换为0
                                else:
                                    value = raw_value
                                data_dict[key] = value
                            except ValueError:
                                continue

            # 优先显示完整的多列数据
            if full_data_rows and header_columns:
                # 创建完整的多列DataFrame
                df = pd.DataFrame(full_data_rows)

                # 尝试转换数值列
                for col in df.columns:
                    if col in ['销售金额', '销售额', '销量', '数量', '金额']:
                        try:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                        except:
                            pass

                # 按数值列排序（如果存在）
                numeric_cols = ['销售金额', '销售额', '销量', '数量', '金额']
                sort_col = None
                for col in numeric_cols:
                    if col in df.columns:
                        sort_col = col
                        break

                if sort_col:
                    df = df.sort_values(sort_col, ascending=False)

                # 显示完整表格
                st.dataframe(df, use_container_width=True, hide_index=True)

                # 显示统计信息
                col1_ui, col2_ui, col3_ui = st.columns(3)
                with col1_ui:
                    st.metric("📊 数据行数", len(df))
                with col2_ui:
                    if sort_col:
                        st.metric(f"📊 {sort_col}总计", f"{df[sort_col].sum():,.2f}")
                with col3_ui:
                    if sort_col:
                        st.metric(f"📊 {sort_col}平均", f"{df[sort_col].mean():,.2f}")

            elif data_dict:
                # 回退到原有的2列显示逻辑
                query = result.get('query', '').lower()

                # 智能推断列名
                if '产品' in query:
                    col1_name = '产品名称'
                elif '地区' in query:
                    col1_name = '地区'
                elif '类别' in query:
                    col1_name = '类别'
                elif '销售员' in query:
                    col1_name = '销售员'
                else:
                    col1_name = '项目'

                if '销售额' in query:
                    col2_name = '销售额'
                elif '销量' in query:
                    col2_name = '销量'
                elif '数量' in query:
                    col2_name = '数量'
                elif '金额' in query:
                    col2_name = '金额'
                else:
                    col2_name = '数值'

                # 创建DataFrame
                df = pd.DataFrame(list(data_dict.items()), columns=[col1_name, col2_name])

                # 按数值排序（降序）
                df = df.sort_values(col2_name, ascending=False)

                # 显示表格
                st.dataframe(df, use_container_width=True, hide_index=True)

                # 只有在没有生成图表时，才显示自动条形图（应用图表持久化修复）
                has_chart = result.get('has_chart', False)
                uses_plotly_native = result.get('uses_plotly_native', False)
                uses_streamlit_native = result.get('uses_streamlit_native', False)
                # 如果使用了任何原生图表或有其他图表，都不显示自动柱状图
                if len(df) <= 15 and not has_chart and not uses_plotly_native and not uses_streamlit_native:
                    st.subheader("📊 可视化")

                    # 使用容器确保图表持久化显示（修复图表消失问题）
                    with st.container():
                        try:
                            chart_data = df.set_index(col1_name)[col2_name]

                            # 深度数据清理（解决Vega-Lite渲染问题）
                            import numpy as np
                            chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
                            chart_data = chart_data.fillna(0)
                            chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

                            # 处理重复索引（避免Vega-Lite字段冲突）
                            if chart_data.index.duplicated().any():
                                chart_data = chart_data.groupby(chart_data.index).sum()

                            # 处理过大的值（避免Vega-Lite scale binding问题）
                            if chart_data.max() > 1e15:
                                chart_data = chart_data / 1e6  # 转换为百万单位
                                value_unit = "（百万）"
                            else:
                                value_unit = ""

                            if not chart_data.empty and chart_data.sum() != 0:
                                # 使用try-except确保图表渲染稳定
                                try:
                                    st.bar_chart(chart_data, use_container_width=True)
                                    if value_unit:
                                        st.caption(f"💡 数值已转换为百万单位显示")
                                except Exception as render_error:
                                    st.error(f"图表渲染失败: {render_error}")
                                    # 备用显示方案
                                    st.dataframe(chart_data.to_frame(f'{col2_name}{value_unit}'))
                            else:
                                st.info("数据无法生成图表")
                        except Exception as e:
                            st.warning(f"图表生成失败: {e}")
                            # 显示数据表格作为备用
                            pass

                # 显示统计信息
                col1_ui, col2_ui, col3_ui = st.columns(3)
                with col1_ui:
                    st.metric("📊 项目数量", len(df))
                with col2_ui:
                    st.metric(f"📊 {col2_name}总计", f"{df[col2_name].sum():,.2f}")
                with col3_ui:
                    st.metric(f"📊 {col2_name}平均", f"{df[col2_name].mean():,.2f}")
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_clean_dataframe(df, inline_mode=False):
        """显示清理后的DataFrame - 统一的DataFrame显示方法 (Streamlit 1.45.0兼容)"""
        try:
            # 确保DataFrame不为空
            if df is None or df.empty:
                st.info("📊 数据为空")
                return

            # 数据清理 - 针对Streamlit 1.45.0优化
            df_clean = df.copy()

            # 1. 确保列名是字符串（新版本要求更严格）
            df_clean.columns = [str(col).strip() for col in df_clean.columns]

            # 2. 处理数值列中的异常值
            numeric_cols = df_clean.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                # 替换无穷大值
                df_clean[col] = df_clean[col].replace([np.inf, -np.inf], np.nan)
                # 使用更保守的填充策略
                if df_clean[col].notna().any():
                    df_clean[col] = df_clean[col].fillna(df_clean[col].median())
                else:
                    df_clean[col] = df_clean[col].fillna(0)

            # 3. 处理文本列（确保类型一致性）
            text_cols = df_clean.select_dtypes(include=['object']).columns
            for col in text_cols:
                df_clean[col] = df_clean[col].astype(str)
                df_clean[col] = df_clean[col].replace('nan', '').replace('None', '')

            # 4. 重置索引（如果有重复）
            if df_clean.index.duplicated().any():
                df_clean = df_clean.reset_index(drop=True)

            # 使用容器确保稳定显示 - 适配Streamlit 1.45.0
            with st.container():
                # 显示DataFrame - 明确设置参数以适配新版本默认行为
                st.dataframe(
                    df_clean,
                    use_container_width=True,  # 新版本默认为True，明确设置
                    hide_index=True if len(df_clean) > 10 else False
                )

                # 显示数据摘要
                if not inline_mode:
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("📊 数据行数", f"{len(df_clean):,}")
                    with col2:
                        st.metric("📋 数据列数", len(df_clean.columns))
                    with col3:
                        # 如果有数值列，显示数值列数量
                        if len(numeric_cols) > 0:
                            st.metric("🔢 数值列数", len(numeric_cols))
                        else:
                            st.metric("📝 文本列数", len(df_clean.columns) - len(numeric_cols))

                # 如果数据量适中，显示数据类型信息
                if not inline_mode and len(df_clean.columns) <= 10:
                    with st.expander("📋 列信息详情", expanded=False):
                        col_info = []
                        for col in df_clean.columns:
                            col_info.append({
                                '列名': col,
                                '数据类型': str(df_clean[col].dtype),
                                '非空值数量': df_clean[col].count(),
                                '空值数量': df_clean[col].isnull().sum()
                            })

                        info_df = pd.DataFrame(col_info)
                        st.dataframe(info_df, use_container_width=True, hide_index=True)

        except Exception as e:
            st.error(f"DataFrame显示失败: {e}")
            # 备用显示方案
            try:
                st.text(str(df))
            except:
                st.error("无法显示数据")

    @staticmethod
    def _smart_convert_columns(df):
        """智能转换DataFrame列的数据类型"""
        for col in df.columns:
            try:
                # 尝试转换为数值类型
                if df[col].dtype == 'object':
                    # 检查是否所有值都可以转换为数字
                    numeric_series = pd.to_numeric(df[col], errors='coerce')
                    # 如果转换后的非空值比例超过80%，则认为是数值列
                    if numeric_series.notna().sum() / len(df) > 0.8:
                        df[col] = numeric_series.fillna(0)
            except:
                # 如果转换失败，保持原始类型
                pass

    @staticmethod
    def _display_formatted_text_table(output, inline_mode=False):
        """显示格式化的文本表格 - 当DataFrame解析失败时的备用方案"""
        try:
            lines = output.strip().split('\n')

            # 检查是否有表格结构
            if len(lines) > 1:
                # 尝试检测是否有对齐的列
                max_line_length = max(len(line) for line in lines if line.strip())

                if max_line_length > 50:  # 可能是宽表格
                    # 使用代码块显示以保持格式
                    st.code(output, language=None)
                else:
                    # 使用文本显示
                    st.text(output)

                # 添加数据行数信息
                data_lines = [line for line in lines if line.strip()]
                if len(data_lines) > 1:  # 减去表头
                    st.caption(f"📊 包含约 {len(data_lines) - 1} 行数据")
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_correlation_matrix(output, result, inline_mode=False):
        """显示相关性矩阵"""
        if not inline_mode:
            st.subheader("🔗 相关性矩阵")

        try:
            lines = output.strip().split('\n')

            # 找到矩阵数据
            matrix_lines = []
            headers = []

            for line in lines:
                line = line.strip()
                if line and any(char.isdigit() or char == '-' for char in line):
                    parts = line.split()
                    if len(parts) >= 2:
                        if not headers:
                            # 可能是第一行，包含列名
                            if parts[0] in ['价格', '销量', '库存', '评分', '销售额']:
                                headers = parts[1:]
                                matrix_lines.append([float(x) for x in parts[1:]])
                            else:
                                try:
                                    matrix_lines.append([float(x) for x in parts])
                                except:
                                    continue
                        else:
                            try:
                                matrix_lines.append([float(x) for x in parts[1:]])
                            except:
                                continue

            if matrix_lines and len(matrix_lines) >= 2:
                # 创建相关性矩阵DataFrame
                if not headers:
                    headers = [f'列{i+1}' for i in range(len(matrix_lines[0]))]

                index_names = [f'行{i+1}' for i in range(len(matrix_lines))]

                # 尝试从输出中提取真实的列名
                for line in lines:
                    if any(name in line for name in ['价格', '销量', '库存', '评分', '销售额']):
                        potential_names = re.findall(r'[\u4e00-\u9fff]+', line)
                        if len(potential_names) >= len(headers):
                            headers = potential_names[:len(headers)]
                            index_names = potential_names[:len(matrix_lines)]
                            break

                df_corr = pd.DataFrame(matrix_lines, columns=headers, index=index_names)

                # 显示相关性矩阵
                st.dataframe(df_corr.round(4), use_container_width=True)

                # 显示关键洞察
                st.subheader("💡 关键洞察")

                # 找出强相关关系
                strong_correlations = []
                for i in range(len(df_corr)):
                    for j in range(i+1, len(df_corr.columns)):
                        corr_value = df_corr.iloc[i, j]
                        if abs(corr_value) > 0.7:
                            strong_correlations.append({
                                'var1': df_corr.index[i],
                                'var2': df_corr.columns[j],
                                'correlation': corr_value
                            })

                if strong_correlations:
                    for corr in strong_correlations:
                        if corr['correlation'] > 0:
                            st.success(f"🔗 {corr['var1']} 与 {corr['var2']} 强正相关 ({corr['correlation']:.3f})")
                        else:
                            st.warning(f"🔗 {corr['var1']} 与 {corr['var2']} 强负相关 ({corr['correlation']:.3f})")
                else:
                    st.info("📊 未发现强相关关系 (|r| > 0.7)")
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_formatted_text(output, result, inline_mode=False):
        """显示格式化文本"""
        if not inline_mode:
            st.subheader("📄 分析结果")

        # 检查是否是空输出
        if not output.strip():
            st.info("ℹ️ 分析完成，但没有文本输出")
            return

        # 对于较长的文本，使用代码块显示
        if len(output) > 200:
            st.code(output, language=None)
        else:
            st.text(output)

        # 如果输出很短，可能是简单的确认消息
        if len(output.strip()) < 50:
            st.success("✅ 操作完成")

    @staticmethod
    def _display_plotly_chart(result, inline_mode=False):
        """显示Plotly原生图表"""
        if not result.get('plotly_code'):
            return

        if not inline_mode:
            st.subheader("📊 数据可视化")

        try:
            # 从结果中获取数据和代码
            plotly_code = result.get('plotly_code', '')

            # 提取图表相关的代码行
            code_lines = plotly_code.split('\n')
            chart_lines = []
            data_processing_lines = []

            # 分离数据处理和图表显示代码
            for line in code_lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    if any(keyword in line for keyword in ['import plotly', 'import streamlit', 'px.pie', 'px.bar', 'px.line', 'px.scatter', 'fig =', 'fig.update', 'st.plotly_chart']):
                        chart_lines.append(line)
                    elif any(keyword in line for keyword in ['groupby', 'sum()', 'mean()', 'reset_index()', '= df']):
                        data_processing_lines.append(line)

            # 构建执行环境
            import pandas as pd
            import plotly.express as px
            import streamlit as st

            # 从session_state获取当前数据
            if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
                df = st.session_state.current_data

                # 执行数据处理代码
                exec_globals = {'df': df, 'pd': pd, 'px': px, 'st': st}

                for line in data_processing_lines:
                    if line:
                        exec(line, exec_globals)

                # 执行图表代码
                for line in chart_lines:
                    if line and 'st.plotly_chart' not in line:  # 先执行图表创建，不包括显示
                        exec(line, exec_globals)

                # 最后显示图表
                if 'fig' in exec_globals:
                    fig = exec_globals['fig']
                    st.plotly_chart(fig, use_container_width=True)
                    st.caption("✨ AI生成的交互式图表")
                else:
                    st.error("图表对象创建失败")
            else:
                st.error("数据不可用，无法显示图表")

        except Exception as e:
            st.error(f"图表显示失败: {e}")
            # 显示原始代码作为备用
            with st.expander("查看图表代码"):
                st.code(result.get('plotly_code', ''), language='python')

    @staticmethod
    def _display_user_chart(result, inline_mode=False):
        """显示用户生成的图表"""
        if not result.get('has_chart'):
            return

        if not inline_mode:
            st.subheader("📊 数据可视化")

        # 优先显示matplotlib图表对象
        if result.get('chart_figure'):
            try:
                # 使用Streamlit的pyplot显示，并优化样式
                import matplotlib.pyplot as plt

                # 设置Streamlit风格的图表样式
                plt.style.use('default')  # 使用默认样式，更接近Streamlit

                # 显示图表
                st.pyplot(result['chart_figure'], clear_figure=True, use_container_width=True)
                st.caption("✨ AI生成的数据可视化图表")
                return
            except Exception as e:
                st.warning(f"图表显示失败: {e}")

        # 备用方案：显示保存的图表文件
        if result.get('chart_path'):
            try:
                import os
                if os.path.exists(result['chart_path']):
                    # 使用容器宽度显示，保持一致性
                    st.image(result['chart_path'],
                            caption="✨ AI生成的数据可视化图表",
                            use_column_width=True)
                else:
                    st.warning("图表文件不存在")
            except Exception as e:
                st.warning(f"图表加载失败: {e}")

        # 最后备用方案：检查charts目录中的最新图表
        try:
            import os
            import glob
            charts_dir = 'charts'
            if os.path.exists(charts_dir):
                chart_files = glob.glob(os.path.join(charts_dir, '*.png'))
                if chart_files:
                    # 获取最新的图表文件
                    latest_chart = max(chart_files, key=os.path.getctime)
                    st.image(latest_chart,
                            caption="✨ AI生成的数据可视化图表",
                            use_column_width=True)
        except Exception:
            st.info("📊 图表已生成，但显示时出现问题")

    @staticmethod
    def _display_mixed_data_with_answer(output, result, inline_mode=False):
        """显示包含数据表格和答案的混合输出"""
        if not inline_mode:
            st.subheader("📊 数据分析结果")

        lines = output.strip().split('\n')
        data_lines = []
        answer_lines = []

        # 分离数据和答案
        for line in lines:
            line = line.strip()
            if line:
                # 检查是否是数据行
                if re.match(r'^\d+\s+\S+\s+[\d.-]+', line) or any(header in line for header in ['产品名称', '地区', '类别']):
                    data_lines.append(line)
                # 检查是否是答案行
                elif any(keyword in line for keyword in ['答案:', '最高', '最低', '最大', '最小']):
                    answer_lines.append(line)

        # 显示数据表格
        if data_lines:
            if not inline_mode:
                st.subheader("📊 详细数据")
            # 使用series_data的显示逻辑
            data_output = '\n'.join(data_lines)
            EnhancedResultFormatter._display_series_data(data_output, result, inline_mode)

        # 显示答案
        if answer_lines:
            if not inline_mode:
                st.subheader("🎯 分析结论")
            for answer in answer_lines:
                if '答案:' in answer:
                    st.success(f"💡 {answer}")
                else:
                    st.info(f"📋 {answer}")

        # 如果没有明确的答案，显示原始输出
        if not answer_lines and not data_lines:
            st.text(output)

# 保持向后兼容性的别名
ResultFormatter = EnhancedResultFormatter
