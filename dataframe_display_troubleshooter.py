#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DataFrame显示问题诊断和修复工具
专门用于诊断和修复Streamlit中DataFrame显示问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
import traceback
from pathlib import Path

class DataFrameDisplayTroubleshooter:
    """DataFrame显示问题诊断器"""
    
    @staticmethod
    def diagnose_display_issues():
        """诊断当前DataFrame显示问题"""
        st.title("🔧 DataFrame显示问题诊断工具")
        
        issues_found = []
        recommendations = []
        
        # 1. 检查session_state中的数据
        st.subheader("1️⃣ 检查当前数据状态")
        
        if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
            df = st.session_state.current_data
            st.success(f"✅ 找到数据: {df.shape[0]}行 × {df.shape[1]}列")
            
            # 显示数据基本信息
            with st.expander("📊 数据基本信息", expanded=True):
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("数据行数", f"{df.shape[0]:,}")
                with col2:
                    st.metric("数据列数", df.shape[1])
                with col3:
                    st.metric("内存使用", f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
                
                # 显示列信息
                st.write("**列信息:**")
                col_info = []
                for col in df.columns:
                    col_info.append({
                        '列名': col,
                        '数据类型': str(df[col].dtype),
                        '非空值': df[col].count(),
                        '空值': df[col].isnull().sum(),
                        '唯一值': df[col].nunique()
                    })
                
                col_df = pd.DataFrame(col_info)
                st.dataframe(col_df, use_container_width=True, hide_index=True)
            
            # 2. 检查数据质量问题
            st.subheader("2️⃣ 数据质量检查")
            
            # 检查无穷大值
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            inf_issues = {}
            for col in numeric_cols:
                inf_count = np.isinf(df[col]).sum()
                if inf_count > 0:
                    inf_issues[col] = inf_count
                    issues_found.append(f"列 '{col}' 包含 {inf_count} 个无穷大值")
            
            if inf_issues:
                st.error(f"❌ 发现无穷大值问题: {inf_issues}")
                recommendations.append("使用 df.replace([np.inf, -np.inf], np.nan) 清理无穷大值")
            else:
                st.success("✅ 无无穷大值问题")
            
            # 检查NaN值
            nan_issues = {}
            for col in df.columns:
                nan_count = df[col].isnull().sum()
                if nan_count > len(df) * 0.5:  # 超过50%为NaN
                    nan_issues[col] = nan_count
                    issues_found.append(f"列 '{col}' 包含过多空值: {nan_count}/{len(df)}")
            
            if nan_issues:
                st.warning(f"⚠️ 发现过多空值: {nan_issues}")
                recommendations.append("考虑删除空值过多的列或使用适当的填充策略")
            else:
                st.success("✅ 空值比例正常")
            
            # 检查问题列名
            problematic_cols = []
            for col in df.columns:
                if any(pattern in str(col) for pattern in ['_start', '_end', '_begin', '_finish']):
                    problematic_cols.append(col)
                    issues_found.append(f"发现问题列名: {col}")
            
            if problematic_cols:
                st.error(f"❌ 发现问题列名: {problematic_cols}")
                recommendations.append("重命名或删除包含 '_start', '_end' 等后缀的列")
            else:
                st.success("✅ 列名格式正常")
            
            # 3. 测试DataFrame显示
            st.subheader("3️⃣ DataFrame显示测试")
            
            try:
                with st.container():
                    st.write("**测试 st.dataframe():**")
                    st.dataframe(df.head(5), use_container_width=True)
                    st.success("✅ st.dataframe() 显示正常")
            except Exception as e:
                st.error(f"❌ st.dataframe() 显示失败: {e}")
                issues_found.append(f"st.dataframe() 显示失败: {e}")
                recommendations.append("检查数据类型和特殊字符")
            
            try:
                with st.container():
                    st.write("**测试 st.table():**")
                    st.table(df.head(3))
                    st.success("✅ st.table() 显示正常")
            except Exception as e:
                st.error(f"❌ st.table() 显示失败: {e}")
                issues_found.append(f"st.table() 显示失败: {e}")
                recommendations.append("st.table() 可能不适合大型数据集")
            
            # 4. 测试数据清理
            st.subheader("4️⃣ 数据清理测试")
            
            if st.button("🧹 执行数据清理测试"):
                try:
                    cleaned_df = DataFrameDisplayTroubleshooter.clean_dataframe_for_display(df)
                    
                    st.success("✅ 数据清理成功")
                    
                    with st.expander("清理后的数据预览", expanded=True):
                        st.dataframe(cleaned_df.head(10), use_container_width=True)
                    
                    # 比较清理前后
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write("**清理前:**")
                        st.write(f"- 形状: {df.shape}")
                        st.write(f"- 无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
                        st.write(f"- NaN值: {df.isnull().sum().sum()}")
                    
                    with col2:
                        st.write("**清理后:**")
                        st.write(f"- 形状: {cleaned_df.shape}")
                        st.write(f"- 无穷大值: {np.isinf(cleaned_df.select_dtypes(include=[np.number])).sum().sum()}")
                        st.write(f"- NaN值: {cleaned_df.isnull().sum().sum()}")
                    
                    if st.button("✅ 应用清理到当前数据"):
                        st.session_state.current_data = cleaned_df
                        st.success("数据清理已应用！请刷新页面查看效果。")
                        st.rerun()
                
                except Exception as e:
                    st.error(f"❌ 数据清理失败: {e}")
                    st.code(traceback.format_exc())
        
        else:
            st.error("❌ 未找到当前数据")
            issues_found.append("session_state中没有current_data")
            recommendations.append("请先上传数据文件")
        
        # 5. 显示诊断结果
        st.subheader("5️⃣ 诊断结果总结")
        
        if issues_found:
            st.error("🚨 发现以下问题:")
            for i, issue in enumerate(issues_found, 1):
                st.write(f"{i}. {issue}")
        else:
            st.success("🎉 未发现显示问题！")
        
        if recommendations:
            st.info("💡 建议采取以下措施:")
            for i, rec in enumerate(recommendations, 1):
                st.write(f"{i}. {rec}")
        
        return issues_found, recommendations
    
    @staticmethod
    def clean_dataframe_for_display(df):
        """清理DataFrame以确保正确显示"""
        if df is None or df.empty:
            return df
        
        cleaned_df = df.copy()
        
        # 1. 移除问题列
        problematic_patterns = ['_start', '_end', '_begin', '_finish']
        cols_to_remove = []
        
        for col in cleaned_df.columns:
            for pattern in problematic_patterns:
                if pattern in str(col):
                    cols_to_remove.append(col)
                    break
        
        if cols_to_remove:
            cleaned_df = cleaned_df.drop(columns=cols_to_remove)
        
        # 2. 清理数值列
        numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            # 替换无穷大值
            cleaned_df[col] = cleaned_df[col].replace([np.inf, -np.inf], np.nan)
            # 填充NaN值
            cleaned_df[col] = cleaned_df[col].fillna(0)
            # 确保数据类型正确
            cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
        
        # 3. 清理列名
        cleaned_df.columns = [str(col).strip() for col in cleaned_df.columns]
        
        # 4. 重置索引
        if cleaned_df.index.duplicated().any():
            cleaned_df = cleaned_df.reset_index(drop=True)
        
        return cleaned_df
    
    @staticmethod
    def test_display_methods():
        """测试不同的DataFrame显示方法"""
        st.subheader("🧪 DataFrame显示方法测试")
        
        if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
            df = st.session_state.current_data
            test_df = df.head(5)  # 使用前5行进行测试
            
            methods = {
                'st.dataframe()': lambda: st.dataframe(test_df, use_container_width=True),
                'st.table()': lambda: st.table(test_df),
                'st.write()': lambda: st.write(test_df),
                'HTML表格': lambda: st.markdown(test_df.to_html(escape=False), unsafe_allow_html=True)
            }
            
            for method_name, method_func in methods.items():
                st.write(f"**测试 {method_name}:**")
                try:
                    with st.container():
                        method_func()
                    st.success(f"✅ {method_name} 显示成功")
                except Exception as e:
                    st.error(f"❌ {method_name} 显示失败: {e}")
                
                st.markdown("---")
        else:
            st.error("没有数据可供测试")

def main():
    """主函数"""
    st.set_page_config(
        page_title="DataFrame显示诊断工具",
        page_icon="🔧",
        layout="wide"
    )
    
    # 运行诊断
    issues, recommendations = DataFrameDisplayTroubleshooter.diagnose_display_issues()
    
    # 显示测试方法
    st.markdown("---")
    DataFrameDisplayTroubleshooter.test_display_methods()

if __name__ == "__main__":
    main()
